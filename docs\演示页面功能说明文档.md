# 演示页面功能说明文档

## 概述

本文档详细说明了用户登录系统演示页面（`yanshiye.html`）的功能实现，包括平台账号管理、SSE实时推送、用户认证等核心功能的API接口和数据格式。

## 目录

1. [用户认证系统](#用户认证系统)
2. [平台账号管理](#平台账号管理)
3. [SSE实时推送](#sse实时推送)
4. [子账号管理](#子账号管理)
5. [激活码系统](#激活码系统)
6. [数据格式说明](#数据格式说明)

---

## 用户认证系统

### 登录接口

**端点**: `POST /auth/login`

**请求参数**:
```json
{
  "phone": "***********",
  "password": "password123",
  "clientVersion": "1.0.0",
  "forceLogin": false
}
```

**响应数据**:
```json
{
  "success": true,
  "sessionId": "session_abc123",
  "user": {
    "id": 123,
    "phone": "***********",
    "account_type": "主账号",
    "owner_phone": null,
    "expiry_date": "2024-12-31 23:59:59",
    "sub_accounts": []
  },
  "requireForceLogin": false,
  "needUpgrade": false,
  "latestVersion": "1.0.0"
}
```

**特殊情况处理**:
- `requireForceLogin: true` - 需要强制登录确认
- `needUpgrade: true` - 客户端版本过低，需要升级

### 注册接口

**端点**: `POST /auth/register`

**请求参数**:
```json
{
  "phone": "***********",
  "password": "password123"
}
```

**响应数据**:
```json
{
  "success": true,
  "message": "注册成功"
}
```

### 会话检查

**端点**: `POST /auth/check-session`

**请求参数**:
```json
{
  "sessionId": "session_abc123"
}
```

**响应数据**:
```json
{
  "success": true,
  "userId": 123,
  "phone": "***********"
}
```

### 退出登录

**端点**: `POST /auth/logout`

**请求参数**:
```json
{
  "sessionId": "session_abc123"
}
```

---

## 平台账号管理

### 添加平台账号

**端点**: `POST /api/user/platform-accounts`

**请求参数**:
```json
{
  "account_data": {
    "phone": "***********",
    "platform": "头条号",
    "login_type": "视频",
    "team_tag": "-",
    "data_update_time": "2024-08-01 10:30:00",
    "login_time": "2024-08-01 10:30:00",
    "username": "-",
    "sessionid": "platform_session_123",
    "homepage_url": "-",
    "is_verified": "否",
    "drafts_count": "-",
    "stats": {
      "followers": "-",
      "total_reads": "-",
      "total_income": "-",
      "yesterday_reads": "-",
      "yesterday_income": "-",
      "credit_score": "-",
      "can_withdraw_amount": "-"
    },
    "account_status": "正常"
  },
  "owner_id": 123,
  "current_holder_id": 123,
  "sessionId": "user_session_abc123"
}
```

**字段说明**:
- `platform`: 平台类型，可选值：`头条号`、`百家号`
- `login_type`: 内容类型，可选值：`视频`、`文章`、`微头条`
- `sessionid`: 平台SessionID，用于平台认证
- `account_status`: 账户状态，可选值：`正常`、`掉线`、`异常`

**响应数据**:
```json
{
  "success": true,
  "message": "平台账号添加成功，收益数据正在获取中..."
}
```

### 获取平台账号列表

**端点**: `GET /api/user/platform-accounts`

**请求参数**:
- `user_id`: 用户ID
- `is_main_account`: 是否为主账号（true/false）
- `sessionId`: 会话ID

**响应数据**:
```json
{
  "success": true,
  "accountInfos": [
    {
      "phone": "***********",
      "platform": "头条号",
      "username": "测试用户",
      "login_type": "视频",
      "team_tag": "A组",
      "is_verified": "是",
      "account_status": "正常",
      "login_time": "2024-08-01 10:30:00",
      "current_holder_id": 123,
      "owner_id": 123,
      "drafts_count": "5",
      "sessionid": "platform_session_123",
      "homepage_url": "https://example.com",
      "data_update_time": "2024-08-01 11:00:00",
      "stats": {
        "followers": "10000",
        "credit_score": "95",
        "total_income": "1500.50",
        "yesterday_income": "25.30",
        "can_withdraw_amount": "1200.00",
        "total_reads": "500000",
        "yesterday_reads": "2500"
      }
    }
  ]
}
```

### 编辑平台账号

**端点**: `PUT /api/user/platform-accounts`

**请求参数**:
```json
{
  "phone": "***********",
  "user_id": 123,
  "is_main_account": true,
  "account_data": {
    // 同添加平台账号的account_data结构
  },
  "sessionId": "user_session_abc123"
}
```

### 删除平台账号

**端点**: `DELETE /api/user/platform-accounts`

**请求参数**:
```json
{
  "phone": "***********",
  "userId": 123,
  "sessionId": "user_session_abc123"
}
```

### 转移平台账号

**端点**: `POST /api/user/transfer-platform-account`

**请求参数**:
```json
{
  "phone": "***********",
  "new_holder_id": 456,
  "user_id": 123,
  "sessionId": "user_session_abc123"
}
```

### 批量转移平台账号

**端点**: `POST /api/user/batch-transfer-platform-accounts`

**请求参数**:
```json
{
  "user_id": 123,
  "transfers": [
    {
      "phone": "***********",
      "newHolderId": 456
    },
    {
      "phone": "***********",
      "newHolderId": 456
    }
  ],
  "sessionId": "user_session_abc123"
}
```

**响应数据**:
```json
{
  "success": true,
  "successCount": 2,
  "failCount": 0,
  "results": [
    {
      "phone": "***********",
      "success": true
    },
    {
      "phone": "***********",
      "success": true
    }
  ]
}
```

---

## SSE实时推送

### 连接建立

**端点**: `GET /auth/sse`

**请求参数**:
- `sessionId`: 会话ID
- `clientId`: 客户端唯一标识

**连接URL示例**:
```
GET /auth/sse?sessionId=session_abc123&clientId=client_xyz789
```

### 推送消息类型

#### 1. 连接确认
```json
{
  "type": "connected",
  "message": "SSE连接已建立"
}
```

#### 2. 强制登出通知
```json
{
  "type": "force_logout",
  "message": "您的账号在其他设备登录，已被强制下线"
}
```

#### 3. 子账号添加平台账号通知
```json
{
  "type": "sub_account_platform_added",
  "message": "子账号 *********** 添加了新的平台账号",
  "data": {
    "subAccountPhone": "***********",
    "platformPhone": "***********"
  }
}
```

#### 4. 平台账号转移/添加接收通知
```json
{
  "type": "platform_account_received",
  "message": "您收到了来自 *********** 转移的平台账号 ***********",
  "data": {
    "fromUserPhone": "***********",
    "platformPhone": "***********",
    "addedByUserType": "用户"
  }
}
```

#### 5. 批量平台账号转移通知
```json
{
  "type": "platform_accounts_batch_received",
  "message": "您收到了 3 个平台账号的批量转移",
  "data": {
    "count": 3,
    "operatorType": "用户",
    "fromUserPhone": "***********"
  }
}
```

#### 6. 平台数据更新通知
```json
{
  "type": "platform_data_update",
  "message": "平台数据已更新",
  "data": {
    "updateType": "stats",
    "platformPhone": "***********"
  }
}
```

#### 7. 平台账号删除通知
```json
{
  "type": "platform_account_deleted_notification",
  "message": "平台账号 *********** 已被删除",
  "data": {
    "platformPhone": "***********",
    "deletedByUserType": "用户",
    "isCurrentUser": false
  }
}
```

---

## 子账号管理

### 获取子账号列表

**端点**: `GET /api/user/sub-accounts`

**请求参数**:
- `ownerId`: 主账号ID
- `sessionId`: 会话ID

**响应数据**:
```json
{
  "success": true,
  "subAccounts": [
    {
      "id": 456,
      "phone": "***********",
      "created_at": "2024-07-01 10:00:00",
      "last_login_at": "2024-08-01 09:30:00"
    }
  ]
}
```

### 创建子账号

**端点**: `POST /api/user/create-sub-account`

**请求参数**:
```json
{
  "ownerId": 123,
  "phone": "***********",
  "password": "password123",
  "sessionId": "user_session_abc123"
}
```

### 删除子账号

**端点**: `POST /api/user/delete-sub-account`

**请求参数**:
```json
{
  "ownerId": 123,
  "subAccountId": 456,
  "sessionId": "user_session_abc123"
}
```

---

## 激活码系统

### 激活码使用

**端点**: `POST /auth/activate-code`

**请求参数**:
```json
{
  "sessionId": "user_session_abc123",
  "code": "ACTIVATE123"
}
```

**响应数据**:
```json
{
  "success": true,
  "days": 30,
  "message": "激活成功！已添加30天会员"
}
```

---

## 数据格式说明

### 用户信息结构
```json
{
  "id": 123,
  "phone": "***********",
  "account_type": "主账号",
  "owner_phone": null,
  "expiry_date": "2024-12-31 23:59:59",
  "sub_accounts": []
}
```

### 平台账号统计数据结构
```json
{
  "followers": "10000",
  "credit_score": "95",
  "total_income": "1500.50",
  "yesterday_income": "25.30",
  "can_withdraw_amount": "1200.00",
  "total_reads": "500000",
  "yesterday_reads": "2500"
}
```

### 错误响应格式
```json
{
  "success": false,
  "message": "错误描述信息",
  "code": "ERROR_CODE"
}
```

---

## 前端实现要点

### 1. 会话管理
- 使用localStorage存储sessionId
- 页面加载时自动检查会话有效性
- 会话失效时自动跳转到登录页面

### 2. SSE连接管理
- 登录成功后自动建立SSE连接
- 连接断开时自动重连
- 退出登录时主动关闭连接

### 3. 数据刷新策略
- SSE推送触发的数据刷新延迟1秒执行
- 用户操作后立即刷新相关数据
- 搜索和筛选基于本地数据进行

### 4. 权限控制
- 主账号可以管理所有平台账号
- 子账号只能管理自己添加的平台账号
- 转移功能仅主账号可用

### 5. 用户体验优化
- 模态框外部点击关闭
- 表单验证和错误提示
- 批量操作进度反馈
- 实时搜索功能

---

## 注意事项

1. **安全性**: 所有API请求都需要携带有效的sessionId
2. **数据一致性**: SSE推送确保多端数据同步
3. **错误处理**: 网络错误和业务错误分别处理
4. **性能优化**: 大量数据时使用分页或虚拟滚动
5. **兼容性**: 确保SSE在不同浏览器中的兼容性

---

*文档版本: v1.0*  
*最后更新: 2024-08-01*
